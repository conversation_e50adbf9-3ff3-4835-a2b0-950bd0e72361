
import csv
from py2neo import Graph,Node,Relationship

# 连接 Docker 内的 Neo4j
g = Graph("bolt://localhost:7687", auth=("neo4j", "neo4j1234"))

with open(r"triples.csv",'r',encoding='utf-8') as f:
    reader=csv.reader(f)
    # 遍历每一行数据
    for item in reader:
        # 首行为表头，不计入关系
        if reader.line_num==1:
            continue
        # 定义起始节点
        start_node = Node("Person", name=item[0])
        end_node = Node("Person", name=item[1])
        # 定义起始节点指向终止节点的关系
        relation = Relationship(start_node, item[2], end_node)
        # 添加起始节点、终止节点、两者关系
        g.merge(start_node, "Person", "name")
        g.merge(end_node, "Person", "name")
        g.merge(relation, "Person", "name")
