
import csv
import os
import json
import logging
from datetime import datetime
from collections import defaultdict, Counter
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib
from py2neo import Graph, Node, Relationship

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HonglouNetworkAnalyzer:
    """红楼梦人物关系网络分析器"""

    def __init__(self, neo4j_uri="bolt://localhost:7687", auth=("neo4j", "neo4j1234")):
        """初始化网络分析器"""
        self.neo4j_uri = neo4j_uri
        self.auth = auth
        self.graph = None
        self.nx_graph = None
        self.result_dir = "result"

        # 创建结果目录
        self._create_result_directory()

    def _create_result_directory(self):
        """创建结果保存目录"""
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
            logger.info(f"创建结果目录: {self.result_dir}")

    def connect_neo4j(self):
        """连接Neo4j数据库"""
        try:
            self.graph = Graph(self.neo4j_uri, auth=self.auth)
            logger.info("成功连接到Neo4j数据库")
            return True
        except Exception as e:
            logger.error(f"连接Neo4j数据库失败: {e}")
            return False

    def load_data_from_csv(self, csv_file="triples.csv"):
        """从CSV文件加载数据并构建网络"""
        if not self.graph:
            logger.error("请先连接Neo4j数据库")
            return False

        try:
            # 清空现有数据
            self.graph.run("MATCH (n) DETACH DELETE n")
            logger.info("清空现有数据")

            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                node_count = 0
                relation_count = 0

                for item in reader:
                    # 跳过表头
                    if reader.line_num == 1:
                        continue

                    if len(item) < 3:
                        logger.warning(f"第{reader.line_num}行数据不完整: {item}")
                        continue

                    # 创建节点和关系
                    start_node = Node("Person", name=item[0])
                    end_node = Node("Person", name=item[1])
                    relation = Relationship(start_node, item[2], end_node)

                    # 合并到图数据库
                    self.graph.merge(start_node, "Person", "name")
                    self.graph.merge(end_node, "Person", "name")
                    self.graph.merge(relation, "Person", "name")

                    relation_count += 1

                    if relation_count % 50 == 0:
                        logger.info(f"已处理 {relation_count} 条关系")

                # 统计节点数
                node_count = self.graph.run("MATCH (n:Person) RETURN count(n) as count").data()[0]['count']

                logger.info(f"数据加载完成: {node_count} 个节点, {relation_count} 条关系")
                return True

        except FileNotFoundError:
            logger.error(f"找不到文件: {csv_file}")
            return False
        except Exception as e:
            logger.error(f"加载数据时发生错误: {e}")
            return False

    def build_networkx_graph(self):
        """从Neo4j构建NetworkX图用于分析"""
        if not self.graph:
            logger.error("请先连接Neo4j数据库")
            return False

        try:
            # 创建NetworkX图
            self.nx_graph = nx.Graph()

            # 获取所有节点
            nodes_query = "MATCH (n:Person) RETURN n.name as name"
            nodes_result = self.graph.run(nodes_query).data()

            for node in nodes_result:
                self.nx_graph.add_node(node['name'])

            # 获取所有关系
            relations_query = """
            MATCH (a:Person)-[r]->(b:Person)
            RETURN a.name as source, b.name as target, type(r) as relation
            """
            relations_result = self.graph.run(relations_query).data()

            for rel in relations_result:
                self.nx_graph.add_edge(rel['source'], rel['target'],
                                     relation=rel['relation'])

            logger.info(f"NetworkX图构建完成: {self.nx_graph.number_of_nodes()} 个节点, "
                       f"{self.nx_graph.number_of_edges()} 条边")
            return True

        except Exception as e:
            logger.error(f"构建NetworkX图时发生错误: {e}")
            return False

    def calculate_network_statistics(self):
        """计算网络统计特征"""
        if not self.nx_graph:
            logger.error("请先构建NetworkX图")
            return None

        try:
            stats = {}

            # 基本统计
            stats['basic'] = {
                'nodes_count': self.nx_graph.number_of_nodes(),
                'edges_count': self.nx_graph.number_of_edges(),
                'density': nx.density(self.nx_graph),
                'is_connected': nx.is_connected(self.nx_graph),
                'connected_components': nx.number_connected_components(self.nx_graph)
            }

            # 度统计
            degrees = dict(self.nx_graph.degree())
            stats['degree'] = {
                'average_degree': sum(degrees.values()) / len(degrees),
                'max_degree': max(degrees.values()),
                'min_degree': min(degrees.values()),
                'degree_distribution': dict(Counter(degrees.values()))
            }

            # 中心性指标
            stats['centrality'] = {
                'degree_centrality': nx.degree_centrality(self.nx_graph),
                'betweenness_centrality': nx.betweenness_centrality(self.nx_graph),
                'closeness_centrality': nx.closeness_centrality(self.nx_graph),
                'eigenvector_centrality': nx.eigenvector_centrality(self.nx_graph, max_iter=1000)
            }

            # 路径统计
            if nx.is_connected(self.nx_graph):
                stats['path'] = {
                    'average_shortest_path_length': nx.average_shortest_path_length(self.nx_graph),
                    'diameter': nx.diameter(self.nx_graph),
                    'radius': nx.radius(self.nx_graph)
                }
            else:
                # 对于非连通图，计算最大连通分量的路径统计
                largest_cc = max(nx.connected_components(self.nx_graph), key=len)
                subgraph = self.nx_graph.subgraph(largest_cc)
                stats['path'] = {
                    'largest_component_size': len(largest_cc),
                    'average_shortest_path_length': nx.average_shortest_path_length(subgraph),
                    'diameter': nx.diameter(subgraph),
                    'radius': nx.radius(subgraph)
                }

            # 聚类系数
            stats['clustering'] = {
                'average_clustering': nx.average_clustering(self.nx_graph),
                'clustering_coefficients': nx.clustering(self.nx_graph)
            }

            logger.info("网络统计特征计算完成")
            return stats

        except Exception as e:
            logger.error(f"计算网络统计特征时发生错误: {e}")
            return None

    def save_statistics_to_files(self, stats):
        """保存统计结果到文件"""
        if not stats:
            logger.error("没有统计数据可保存")
            return False

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存为JSON格式
            json_file = os.path.join(self.result_dir, f"network_stats_{timestamp}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"统计结果已保存为JSON: {json_file}")

            # 保存基本统计为CSV
            basic_stats_file = os.path.join(self.result_dir, f"basic_stats_{timestamp}.csv")
            basic_df = pd.DataFrame([stats['basic']])
            basic_df.to_csv(basic_stats_file, index=False, encoding='utf-8-sig')
            logger.info(f"基本统计已保存为CSV: {basic_stats_file}")

            # 保存度中心性排名
            degree_centrality_file = os.path.join(self.result_dir, f"degree_centrality_{timestamp}.csv")
            degree_centrality_df = pd.DataFrame(list(stats['centrality']['degree_centrality'].items()),
                                              columns=['人物', '度中心性'])
            degree_centrality_df = degree_centrality_df.sort_values('度中心性', ascending=False)
            degree_centrality_df.to_csv(degree_centrality_file, index=False, encoding='utf-8-sig')
            logger.info(f"度中心性排名已保存为CSV: {degree_centrality_file}")

            # 保存介数中心性排名
            betweenness_centrality_file = os.path.join(self.result_dir, f"betweenness_centrality_{timestamp}.csv")
            betweenness_centrality_df = pd.DataFrame(list(stats['centrality']['betweenness_centrality'].items()),
                                                   columns=['人物', '介数中心性'])
            betweenness_centrality_df = betweenness_centrality_df.sort_values('介数中心性', ascending=False)
            betweenness_centrality_df.to_csv(betweenness_centrality_file, index=False, encoding='utf-8-sig')
            logger.info(f"介数中心性排名已保存为CSV: {betweenness_centrality_file}")

            # 保存接近中心性排名
            closeness_centrality_file = os.path.join(self.result_dir, f"closeness_centrality_{timestamp}.csv")
            closeness_centrality_df = pd.DataFrame(list(stats['centrality']['closeness_centrality'].items()),
                                                 columns=['人物', '接近中心性'])
            closeness_centrality_df = closeness_centrality_df.sort_values('接近中心性', ascending=False)
            closeness_centrality_df.to_csv(closeness_centrality_file, index=False, encoding='utf-8-sig')
            logger.info(f"接近中心性排名已保存为CSV: {closeness_centrality_file}")

            # 保存特征向量中心性排名
            eigenvector_centrality_file = os.path.join(self.result_dir, f"eigenvector_centrality_{timestamp}.csv")
            eigenvector_centrality_df = pd.DataFrame(list(stats['centrality']['eigenvector_centrality'].items()),
                                                   columns=['人物', '特征向量中心性'])
            eigenvector_centrality_df = eigenvector_centrality_df.sort_values('特征向量中心性', ascending=False)
            eigenvector_centrality_df.to_csv(eigenvector_centrality_file, index=False, encoding='utf-8-sig')
            logger.info(f"特征向量中心性排名已保存为CSV: {eigenvector_centrality_file}")

            # 保存度分布
            degree_distribution_file = os.path.join(self.result_dir, f"degree_distribution_{timestamp}.csv")
            degree_dist_df = pd.DataFrame(list(stats['degree']['degree_distribution'].items()),
                                        columns=['度数', '节点数量'])
            degree_dist_df = degree_dist_df.sort_values('度数')
            degree_dist_df.to_csv(degree_distribution_file, index=False, encoding='utf-8-sig')
            logger.info(f"度分布已保存为CSV: {degree_distribution_file}")

            return True

        except Exception as e:
            logger.error(f"保存统计结果时发生错误: {e}")
            return False

    def visualize_network(self, stats=None, save_plots=True):
        """可视化网络"""
        if not self.nx_graph:
            logger.error("请先构建NetworkX图")
            return False

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 1. 绘制网络图
            plt.figure(figsize=(20, 16))

            # 使用spring布局
            pos = nx.spring_layout(self.nx_graph, k=3, iterations=50)

            # 根据度中心性设置节点大小
            if stats and 'centrality' in stats:
                node_sizes = [stats['centrality']['degree_centrality'].get(node, 0) * 3000 + 100
                            for node in self.nx_graph.nodes()]
            else:
                node_sizes = [self.nx_graph.degree(node) * 100 + 100 for node in self.nx_graph.nodes()]

            # 绘制网络
            nx.draw(self.nx_graph, pos,
                   node_size=node_sizes,
                   node_color='lightblue',
                   edge_color='gray',
                   with_labels=True,
                   font_size=8,
                   font_family='SimHei',
                   alpha=0.7)

            plt.title("红楼梦人物关系网络图", fontsize=16, fontweight='bold')

            if save_plots:
                network_plot_file = os.path.join(self.result_dir, f"network_graph_{timestamp}.png")
                plt.savefig(network_plot_file, dpi=300, bbox_inches='tight')
                logger.info(f"网络图已保存: {network_plot_file}")

            plt.show()

            # 2. 绘制度分布图
            if stats and 'degree' in stats:
                plt.figure(figsize=(12, 8))

                degrees = list(stats['degree']['degree_distribution'].keys())
                counts = list(stats['degree']['degree_distribution'].values())

                plt.bar(degrees, counts, alpha=0.7, color='skyblue')
                plt.xlabel('度数', fontsize=12)
                plt.ylabel('节点数量', fontsize=12)
                plt.title('度分布图', fontsize=14, fontweight='bold')
                plt.grid(True, alpha=0.3)

                if save_plots:
                    degree_plot_file = os.path.join(self.result_dir, f"degree_distribution_{timestamp}.png")
                    plt.savefig(degree_plot_file, dpi=300, bbox_inches='tight')
                    logger.info(f"度分布图已保存: {degree_plot_file}")

                plt.show()

            # 3. 绘制中心性对比图
            if stats and 'centrality' in stats:
                plt.figure(figsize=(15, 10))

                # 选择前10个度中心性最高的节点
                top_nodes = sorted(stats['centrality']['degree_centrality'].items(),
                                 key=lambda x: x[1], reverse=True)[:10]

                nodes = [item[0] for item in top_nodes]
                degree_cent = [stats['centrality']['degree_centrality'][node] for node in nodes]
                betweenness_cent = [stats['centrality']['betweenness_centrality'][node] for node in nodes]
                closeness_cent = [stats['centrality']['closeness_centrality'][node] for node in nodes]
                eigenvector_cent = [stats['centrality']['eigenvector_centrality'][node] for node in nodes]

                x = range(len(nodes))
                width = 0.2

                plt.bar([i - 1.5*width for i in x], degree_cent, width, label='度中心性', alpha=0.8)
                plt.bar([i - 0.5*width for i in x], betweenness_cent, width, label='介数中心性', alpha=0.8)
                plt.bar([i + 0.5*width for i in x], closeness_cent, width, label='接近中心性', alpha=0.8)
                plt.bar([i + 1.5*width for i in x], eigenvector_cent, width, label='特征向量中心性', alpha=0.8)

                plt.xlabel('人物', fontsize=12)
                plt.ylabel('中心性值', fontsize=12)
                plt.title('主要人物中心性对比', fontsize=14, fontweight='bold')
                plt.xticks(x, nodes, rotation=45, ha='right')
                plt.legend()
                plt.grid(True, alpha=0.3)

                if save_plots:
                    centrality_plot_file = os.path.join(self.result_dir, f"centrality_comparison_{timestamp}.png")
                    plt.savefig(centrality_plot_file, dpi=300, bbox_inches='tight')
                    logger.info(f"中心性对比图已保存: {centrality_plot_file}")

                plt.show()

            return True

        except Exception as e:
            logger.error(f"可视化网络时发生错误: {e}")
            return False

    def generate_summary_report(self, stats):
        """生成分析报告摘要"""
        if not stats:
            return None

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 找出重要人物
            top_degree = sorted(stats['centrality']['degree_centrality'].items(),
                              key=lambda x: x[1], reverse=True)[:5]
            top_betweenness = sorted(stats['centrality']['betweenness_centrality'].items(),
                                   key=lambda x: x[1], reverse=True)[:5]
            top_closeness = sorted(stats['centrality']['closeness_centrality'].items(),
                                 key=lambda x: x[1], reverse=True)[:5]
            top_eigenvector = sorted(stats['centrality']['eigenvector_centrality'].items(),
                                   key=lambda x: x[1], reverse=True)[:5]

            report = f"""
红楼梦人物关系网络分析报告
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

=== 网络基本信息 ===
节点数量: {stats['basic']['nodes_count']}
边数量: {stats['basic']['edges_count']}
网络密度: {stats['basic']['density']:.4f}
是否连通: {'是' if stats['basic']['is_connected'] else '否'}
连通分量数: {stats['basic']['connected_components']}

=== 度统计 ===
平均度: {stats['degree']['average_degree']:.2f}
最大度: {stats['degree']['max_degree']}
最小度: {stats['degree']['min_degree']}

=== 路径统计 ===
"""

            if 'path' in stats:
                if 'average_shortest_path_length' in stats['path']:
                    report += f"平均最短路径长度: {stats['path']['average_shortest_path_length']:.2f}\n"
                if 'diameter' in stats['path']:
                    report += f"网络直径: {stats['path']['diameter']}\n"
                if 'radius' in stats['path']:
                    report += f"网络半径: {stats['path']['radius']}\n"
                if 'largest_component_size' in stats['path']:
                    report += f"最大连通分量大小: {stats['path']['largest_component_size']}\n"

            report += f"""
=== 聚类统计 ===
平均聚类系数: {stats['clustering']['average_clustering']:.4f}

=== 重要人物排名 ===

度中心性前5名:
"""
            for i, (name, value) in enumerate(top_degree, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            report += "\n介数中心性前5名:\n"
            for i, (name, value) in enumerate(top_betweenness, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            report += "\n接近中心性前5名:\n"
            for i, (name, value) in enumerate(top_closeness, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            report += "\n特征向量中心性前5名:\n"
            for i, (name, value) in enumerate(top_eigenvector, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            # 保存报告
            report_file = os.path.join(self.result_dir, f"analysis_report_{timestamp}.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            logger.info(f"分析报告已保存: {report_file}")
            print(report)

            return report

        except Exception as e:
            logger.error(f"生成分析报告时发生错误: {e}")
            return None

    def run_complete_analysis(self, csv_file="triples.csv", visualize=True, use_neo4j=True):
        """运行完整的网络分析流程"""
        logger.info("开始红楼梦人物关系网络分析")

        if use_neo4j:
            # 1. 连接数据库
            if not self.connect_neo4j():
                logger.warning("Neo4j连接失败，将使用纯NetworkX模式进行分析")
                return self.run_networkx_only_analysis(csv_file, visualize)
        else:
            return self.run_networkx_only_analysis(csv_file, visualize)

        # 2. 加载数据
        if not self.load_data_from_csv(csv_file):
            return False

        # 3. 构建NetworkX图
        if not self.build_networkx_graph():
            return False

        # 4. 计算统计特征
        stats = self.calculate_network_statistics()
        if not stats:
            return False

        # 5. 保存统计结果
        if not self.save_statistics_to_files(stats):
            return False

        # 6. 生成可视化图表
        if visualize:
            self.visualize_network(stats)

        # 7. 生成分析报告
        self.generate_summary_report(stats)

        logger.info("网络分析完成！所有结果已保存到result文件夹")
        return True

    def run_networkx_only_analysis(self, csv_file="triples.csv", visualize=True):
        """使用纯NetworkX进行分析（不依赖Neo4j）"""
        logger.info("使用纯NetworkX模式进行分析")

        # 1. 直接从CSV构建NetworkX图
        if not self.build_networkx_from_csv(csv_file):
            return False

        # 2. 计算统计特征
        stats = self.calculate_network_statistics()
        if not stats:
            return False

        # 3. 保存统计结果
        if not self.save_statistics_to_files(stats):
            return False

        # 4. 生成可视化图表
        if visualize:
            self.visualize_network(stats)

        # 5. 生成分析报告
        self.generate_summary_report(stats)

        logger.info("NetworkX模式分析完成！所有结果已保存到result文件夹")
        return True

    def build_networkx_from_csv(self, csv_file="triples.csv"):
        """直接从CSV文件构建NetworkX图"""
        try:
            self.nx_graph = nx.Graph()

            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                relation_count = 0

                for item in reader:
                    # 跳过表头
                    if reader.line_num == 1:
                        continue

                    if len(item) < 3:
                        logger.warning(f"第{reader.line_num}行数据不完整: {item}")
                        continue

                    # 添加边（会自动添加节点）
                    self.nx_graph.add_edge(item[0], item[1], relation=item[2])
                    relation_count += 1

                    if relation_count % 50 == 0:
                        logger.info(f"已处理 {relation_count} 条关系")

            logger.info(f"NetworkX图构建完成: {self.nx_graph.number_of_nodes()} 个节点, "
                       f"{self.nx_graph.number_of_edges()} 条边")
            return True

        except FileNotFoundError:
            logger.error(f"找不到文件: {csv_file}")
            return False
        except Exception as e:
            logger.error(f"构建NetworkX图时发生错误: {e}")
            return False


def main():
    """主函数"""
    print("=== 红楼梦人物关系网络分析工具 ===")
    print("1. 使用Neo4j + NetworkX模式（需要Neo4j数据库）")
    print("2. 使用纯NetworkX模式（无需Neo4j数据库）")
    print("3. 自动选择（优先Neo4j，失败则使用NetworkX）")

    while True:
        try:
            choice = input("\n请选择分析模式 (1/2/3，默认为3): ").strip()
            if choice == "" or choice == "3":
                use_neo4j = True
                auto_fallback = True
                break
            elif choice == "1":
                use_neo4j = True
                auto_fallback = False
                break
            elif choice == "2":
                use_neo4j = False
                auto_fallback = False
                break
            else:
                print("请输入有效选项 (1/2/3)")
        except KeyboardInterrupt:
            print("\n程序已取消")
            return

    # 创建分析器实例
    analyzer = HonglouNetworkAnalyzer()

    # 运行分析
    if use_neo4j and not auto_fallback:
        # 强制使用Neo4j模式
        success = analyzer.run_complete_analysis(use_neo4j=True)
    elif not use_neo4j:
        # 纯NetworkX模式
        success = analyzer.run_complete_analysis(use_neo4j=False)
    else:
        # 自动选择模式
        success = analyzer.run_complete_analysis(use_neo4j=True)

    if success:
        print("\n✅ 分析完成！请查看result文件夹中的结果文件。")
        print("\n主要输出文件：")
        print("- network_stats_*.json: 完整统计数据")
        print("- *_centrality_*.csv: 各种中心性排名")
        print("- network_graph_*.png: 网络关系图")
        print("- analysis_report_*.txt: 分析报告摘要")
    else:
        print("\n❌ 分析过程中出现错误，请检查日志信息。")
        print("\n故障排除建议：")
        print("1. 如果是Neo4j连接问题，请确保Neo4j数据库正在运行")
        print("2. 可以选择纯NetworkX模式进行分析")
        print("3. 检查triples.csv文件是否存在且格式正确")


if __name__ == "__main__":
    main()
