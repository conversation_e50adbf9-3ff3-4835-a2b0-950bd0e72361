
import csv
import os
import json
from datetime import datetime
from collections import Counter
import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

class HonglouNetworkAnalyzer:
    """红楼梦人物关系网络分析器"""

    def __init__(self):
        """初始化网络分析器"""
        self.nx_graph = None
        self.result_dir = "result"
        self._create_result_directory()

    def _create_result_directory(self):
        """创建结果保存目录"""
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
            print(f"创建结果目录: {self.result_dir}")

    def load_data_from_csv(self, csv_file="triples.csv"):
        """从CSV文件构建NetworkX图"""
        try:
            self.nx_graph = nx.Graph()

            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                relation_count = 0

                for item in reader:
                    # 跳过表头
                    if reader.line_num == 1:
                        continue

                    if len(item) < 3:
                        print(f"第{reader.line_num}行数据不完整: {item}")
                        continue

                    # 添加边（会自动添加节点）
                    self.nx_graph.add_edge(item[0], item[1], relation=item[2])
                    relation_count += 1

                    if relation_count % 50 == 0:
                        print(f"已处理 {relation_count} 条关系")

            print(f"数据加载完成: {self.nx_graph.number_of_nodes()} 个节点, "
                  f"{self.nx_graph.number_of_edges()} 条边")
            return True

        except FileNotFoundError:
            print(f"找不到文件: {csv_file}")
            return False
        except Exception as e:
            print(f"加载数据时发生错误: {e}")
            return False

    def calculate_network_statistics(self):
        """计算网络统计特征"""
        if not self.nx_graph:
            print("请先加载数据")
            return None

        try:
            stats = {}

            # 基本统计
            stats['basic'] = {
                'nodes_count': self.nx_graph.number_of_nodes(),
                'edges_count': self.nx_graph.number_of_edges(),
                'density': nx.density(self.nx_graph),
                'is_connected': nx.is_connected(self.nx_graph),
                'connected_components': nx.number_connected_components(self.nx_graph)
            }

            # 度统计
            degrees = dict(self.nx_graph.degree())
            stats['degree'] = {
                'average_degree': sum(degrees.values()) / len(degrees),
                'max_degree': max(degrees.values()),
                'min_degree': min(degrees.values()),
                'degree_distribution': dict(Counter(degrees.values()))
            }

            # 中心性指标
            stats['centrality'] = {
                'degree_centrality': nx.degree_centrality(self.nx_graph),
                'betweenness_centrality': nx.betweenness_centrality(self.nx_graph),
                'closeness_centrality': nx.closeness_centrality(self.nx_graph),
                'eigenvector_centrality': nx.eigenvector_centrality(self.nx_graph, max_iter=1000)
            }

            # 路径统计
            if nx.is_connected(self.nx_graph):
                stats['path'] = {
                    'average_shortest_path_length': nx.average_shortest_path_length(self.nx_graph),
                    'diameter': nx.diameter(self.nx_graph),
                    'radius': nx.radius(self.nx_graph)
                }
            else:
                # 对于非连通图，计算最大连通分量的路径统计
                largest_cc = max(nx.connected_components(self.nx_graph), key=len)
                subgraph = self.nx_graph.subgraph(largest_cc)
                stats['path'] = {
                    'largest_component_size': len(largest_cc),
                    'average_shortest_path_length': nx.average_shortest_path_length(subgraph),
                    'diameter': nx.diameter(subgraph),
                    'radius': nx.radius(subgraph)
                }

            # 聚类系数
            stats['clustering'] = {
                'average_clustering': nx.average_clustering(self.nx_graph),
                'clustering_coefficients': nx.clustering(self.nx_graph)
            }

            print("网络统计特征计算完成")
            return stats

        except Exception as e:
            print(f"计算网络统计特征时发生错误: {e}")
            return None

    def save_statistics_to_files(self, stats):
        """保存统计结果到文件"""
        if not stats:
            print("没有统计数据可保存")
            return False

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存为JSON格式
            json_file = os.path.join(self.result_dir, f"network_stats_{timestamp}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2, default=str)
            print(f"统计结果已保存为JSON: {json_file}")

            # 保存基本统计为CSV
            basic_stats_file = os.path.join(self.result_dir, f"basic_stats_{timestamp}.csv")
            basic_df = pd.DataFrame([stats['basic']])
            basic_df.to_csv(basic_stats_file, index=False, encoding='utf-8-sig')
            print(f"基本统计已保存为CSV: {basic_stats_file}")

            # 保存度中心性排名
            degree_centrality_file = os.path.join(self.result_dir, f"degree_centrality_{timestamp}.csv")
            degree_centrality_df = pd.DataFrame(list(stats['centrality']['degree_centrality'].items()),
                                              columns=['人物', '度中心性'])
            degree_centrality_df = degree_centrality_df.sort_values('度中心性', ascending=False)
            degree_centrality_df.to_csv(degree_centrality_file, index=False, encoding='utf-8-sig')
            print(f"度中心性排名已保存为CSV: {degree_centrality_file}")

            # 保存介数中心性排名
            betweenness_centrality_file = os.path.join(self.result_dir, f"betweenness_centrality_{timestamp}.csv")
            betweenness_centrality_df = pd.DataFrame(list(stats['centrality']['betweenness_centrality'].items()),
                                                   columns=['人物', '介数中心性'])
            betweenness_centrality_df = betweenness_centrality_df.sort_values('介数中心性', ascending=False)
            betweenness_centrality_df.to_csv(betweenness_centrality_file, index=False, encoding='utf-8-sig')
            print(f"介数中心性排名已保存为CSV: {betweenness_centrality_file}")

            # 保存接近中心性排名
            closeness_centrality_file = os.path.join(self.result_dir, f"closeness_centrality_{timestamp}.csv")
            closeness_centrality_df = pd.DataFrame(list(stats['centrality']['closeness_centrality'].items()),
                                                 columns=['人物', '接近中心性'])
            closeness_centrality_df = closeness_centrality_df.sort_values('接近中心性', ascending=False)
            closeness_centrality_df.to_csv(closeness_centrality_file, index=False, encoding='utf-8-sig')
            print(f"接近中心性排名已保存为CSV: {closeness_centrality_file}")

            # 保存特征向量中心性排名
            eigenvector_centrality_file = os.path.join(self.result_dir, f"eigenvector_centrality_{timestamp}.csv")
            eigenvector_centrality_df = pd.DataFrame(list(stats['centrality']['eigenvector_centrality'].items()),
                                                   columns=['人物', '特征向量中心性'])
            eigenvector_centrality_df = eigenvector_centrality_df.sort_values('特征向量中心性', ascending=False)
            eigenvector_centrality_df.to_csv(eigenvector_centrality_file, index=False, encoding='utf-8-sig')
            print(f"特征向量中心性排名已保存为CSV: {eigenvector_centrality_file}")

            # 保存度分布
            degree_distribution_file = os.path.join(self.result_dir, f"degree_distribution_{timestamp}.csv")
            degree_dist_df = pd.DataFrame(list(stats['degree']['degree_distribution'].items()),
                                        columns=['度数', '节点数量'])
            degree_dist_df = degree_dist_df.sort_values('度数')
            degree_dist_df.to_csv(degree_distribution_file, index=False, encoding='utf-8-sig')
            print(f"度分布已保存为CSV: {degree_distribution_file}")

            return True

        except Exception as e:
            print(f"保存统计结果时发生错误: {e}")
            return False

    def visualize_network(self, stats=None):
        """可视化网络"""
        if not self.nx_graph:
            print("请先加载数据")
            return False

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 1. 绘制网络图
            plt.figure(figsize=(16, 12))
            pos = nx.spring_layout(self.nx_graph, k=2, iterations=50)

            # 根据度设置节点大小
            node_sizes = [self.nx_graph.degree(node) * 50 + 100 for node in self.nx_graph.nodes()]

            nx.draw(self.nx_graph, pos,
                   node_size=node_sizes,
                   node_color='lightblue',
                   edge_color='gray',
                   with_labels=True,
                   font_size=6,
                   alpha=0.7)

            plt.title("红楼梦人物关系网络图", fontsize=16)
            network_file = os.path.join(self.result_dir, f"network_graph_{timestamp}.png")
            plt.savefig(network_file, dpi=200, bbox_inches='tight')
            print(f"网络图已保存: {network_file}")
            plt.show()

            # 2. 绘制度分布图
            if stats and 'degree' in stats:
                plt.figure(figsize=(10, 6))
                degrees = list(stats['degree']['degree_distribution'].keys())
                counts = list(stats['degree']['degree_distribution'].values())

                plt.bar(degrees, counts, alpha=0.7, color='skyblue')
                plt.xlabel('度数')
                plt.ylabel('节点数量')
                plt.title('度分布图')
                plt.grid(True, alpha=0.3)

                degree_file = os.path.join(self.result_dir, f"degree_distribution_{timestamp}.png")
                plt.savefig(degree_file, dpi=200, bbox_inches='tight')
                print(f"度分布图已保存: {degree_file}")
                plt.show()

            return True

        except Exception as e:
            print(f"可视化网络时发生错误: {e}")
            return False

    def generate_summary_report(self, stats):
        """生成分析报告摘要"""
        if not stats:
            return None

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 找出重要人物
            top_degree = sorted(stats['centrality']['degree_centrality'].items(),
                              key=lambda x: x[1], reverse=True)[:5]
            top_betweenness = sorted(stats['centrality']['betweenness_centrality'].items(),
                                   key=lambda x: x[1], reverse=True)[:5]
            top_closeness = sorted(stats['centrality']['closeness_centrality'].items(),
                                 key=lambda x: x[1], reverse=True)[:5]
            top_eigenvector = sorted(stats['centrality']['eigenvector_centrality'].items(),
                                   key=lambda x: x[1], reverse=True)[:5]

            report = f"""
红楼梦人物关系网络分析报告
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

=== 网络基本信息 ===
节点数量: {stats['basic']['nodes_count']}
边数量: {stats['basic']['edges_count']}
网络密度: {stats['basic']['density']:.4f}
是否连通: {'是' if stats['basic']['is_connected'] else '否'}
连通分量数: {stats['basic']['connected_components']}

=== 度统计 ===
平均度: {stats['degree']['average_degree']:.2f}
最大度: {stats['degree']['max_degree']}
最小度: {stats['degree']['min_degree']}

=== 路径统计 ===
"""

            if 'path' in stats:
                if 'average_shortest_path_length' in stats['path']:
                    report += f"平均最短路径长度: {stats['path']['average_shortest_path_length']:.2f}\n"
                if 'diameter' in stats['path']:
                    report += f"网络直径: {stats['path']['diameter']}\n"
                if 'radius' in stats['path']:
                    report += f"网络半径: {stats['path']['radius']}\n"
                if 'largest_component_size' in stats['path']:
                    report += f"最大连通分量大小: {stats['path']['largest_component_size']}\n"

            report += f"""
=== 聚类统计 ===
平均聚类系数: {stats['clustering']['average_clustering']:.4f}

=== 重要人物排名 ===

度中心性前5名:
"""
            for i, (name, value) in enumerate(top_degree, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            report += "\n介数中心性前5名:\n"
            for i, (name, value) in enumerate(top_betweenness, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            report += "\n接近中心性前5名:\n"
            for i, (name, value) in enumerate(top_closeness, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            report += "\n特征向量中心性前5名:\n"
            for i, (name, value) in enumerate(top_eigenvector, 1):
                report += f"{i}. {name}: {value:.4f}\n"

            # 保存报告
            report_file = os.path.join(self.result_dir, f"analysis_report_{timestamp}.txt")
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            print(f"分析报告已保存: {report_file}")
            print(report)

            return report

        except Exception as e:
            print(f"生成分析报告时发生错误: {e}")
            return None

    def run_analysis(self, csv_file="triples.csv"):
        """运行完整的网络分析"""
        print("开始红楼梦人物关系网络分析")

        # 1. 加载数据
        if not self.load_data_from_csv(csv_file):
            return False

        # 2. 计算统计特征
        stats = self.calculate_network_statistics()
        if not stats:
            return False

        # 3. 保存统计结果
        if not self.save_statistics_to_files(stats):
            return False

        # 4. 生成可视化图表
        self.visualize_network(stats)

        # 5. 生成分析报告
        self.generate_summary_report(stats)

        print("网络分析完成！所有结果已保存到result文件夹")
        return True


def main():
    """主函数"""
    print("=== 红楼梦人物关系网络分析工具 ===")

    # 创建分析器实例
    analyzer = HonglouNetworkAnalyzer()

    # 运行分析
    success = analyzer.run_analysis()

    if success:
        print("\n✅ 分析完成！请查看result文件夹中的结果文件。")
        print("\n主要输出文件：")
        print("- network_stats_*.json: 完整统计数据")
        print("- *_centrality_*.csv: 各种中心性排名")
        print("- network_graph_*.png: 网络关系图")
        print("- analysis_report_*.txt: 分析报告摘要")
    else:
        print("\n❌ 分析过程中出现错误，请检查triples.csv文件是否存在且格式正确。")


if __name__ == "__main__":
    main()
