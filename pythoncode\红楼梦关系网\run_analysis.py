#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红楼梦人物关系网络分析 - 快速启动脚本
使用纯NetworkX模式，无需Neo4j数据库
"""

from honglou_network import HonglouNetworkAnalyzer
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """快速启动分析（纯NetworkX模式）"""
    print("=== 红楼梦人物关系网络分析（NetworkX模式）===")
    print("正在启动分析...")
    
    # 创建分析器实例
    analyzer = HonglouNetworkAnalyzer()
    
    # 运行纯NetworkX分析
    success = analyzer.run_networkx_only_analysis()
    
    if success:
        print("\n✅ 分析完成！")
        print("📁 结果已保存到 result 文件夹")
        print("\n📊 主要输出文件：")
        print("   • network_stats_*.json - 完整统计数据")
        print("   • *_centrality_*.csv - 中心性排名")
        print("   • network_graph_*.png - 网络关系图")
        print("   • analysis_report_*.txt - 分析报告")
    else:
        print("\n❌ 分析失败，请检查：")
        print("   • triples.csv 文件是否存在")
        print("   • 文件格式是否正确")
        print("   • 是否有足够的磁盘空间")

if __name__ == "__main__":
    main()
