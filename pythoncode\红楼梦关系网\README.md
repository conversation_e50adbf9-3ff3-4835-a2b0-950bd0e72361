# 红楼梦人物关系网络分析

这是一个基于Neo4j图数据库和NetworkX的红楼梦人物关系网络分析工具。

## 功能特性

- 从CSV文件加载人物关系数据到Neo4j数据库
- 计算网络的各种统计特征
- 生成多种中心性指标排名
- 可视化网络图和统计图表
- 自动保存分析结果到result文件夹

## 环境要求

- Python 3.7+
- Neo4j数据库（推荐使用Docker）
- 必要的Python包（见requirements.txt）

## 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 启动Neo4j数据库（使用Docker）：
```bash
docker run -d \
    --name neo4j \
    -p 7474:7474 -p 7687:7687 \
    -e NEO4J_AUTH=neo4j/neo4j1234 \
    neo4j:latest
```

3. 确保triples.csv文件在当前目录下

## 使用方法

直接运行主程序：
```bash
python honglou_network.py
```

程序将自动执行以下步骤：
1. 连接Neo4j数据库
2. 加载CSV数据并构建网络
3. 计算网络统计特征
4. 生成可视化图表
5. 保存所有结果到result文件夹

## 输出文件

程序会在result文件夹中生成以下文件：

### 统计数据文件
- `network_stats_YYYYMMDD_HHMMSS.json` - 完整的网络统计数据
- `basic_stats_YYYYMMDD_HHMMSS.csv` - 基本网络统计
- `degree_centrality_YYYYMMDD_HHMMSS.csv` - 度中心性排名
- `betweenness_centrality_YYYYMMDD_HHMMSS.csv` - 介数中心性排名
- `closeness_centrality_YYYYMMDD_HHMMSS.csv` - 接近中心性排名
- `eigenvector_centrality_YYYYMMDD_HHMMSS.csv` - 特征向量中心性排名
- `degree_distribution_YYYYMMDD_HHMMSS.csv` - 度分布数据

### 可视化图表
- `network_graph_YYYYMMDD_HHMMSS.png` - 网络关系图
- `degree_distribution_YYYYMMDD_HHMMSS.png` - 度分布图
- `centrality_comparison_YYYYMMDD_HHMMSS.png` - 中心性对比图

### 分析报告
- `analysis_report_YYYYMMDD_HHMMSS.txt` - 文本格式的分析报告摘要

## 网络统计指标说明

- **度中心性**: 衡量节点的直接连接数量
- **介数中心性**: 衡量节点在网络中的桥梁作用
- **接近中心性**: 衡量节点到其他节点的平均距离
- **特征向量中心性**: 考虑邻居节点重要性的中心性指标
- **聚类系数**: 衡量节点邻居之间的连接密度
- **网络密度**: 实际边数与可能边数的比例
- **平均路径长度**: 网络中任意两点间最短路径的平均长度

## 注意事项

1. 确保Neo4j数据库正在运行且可访问
2. CSV文件格式应为：source,target,relation,label
3. 程序会清空Neo4j中的现有数据，请注意备份
4. 大型网络的可视化可能需要较长时间

## 故障排除

- 如果连接Neo4j失败，请检查数据库是否启动以及认证信息是否正确
- 如果出现中文显示问题，请确保系统安装了SimHei字体
- 如果内存不足，可以在代码中调整可视化参数或禁用可视化功能
